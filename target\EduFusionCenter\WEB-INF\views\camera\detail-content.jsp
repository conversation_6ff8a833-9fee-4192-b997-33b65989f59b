<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<div class="container-fluid py-4">
    <!-- 返回按钮和操作按钮 -->
    <div class="mb-4 d-flex justify-content-between align-items-center">
        <a href="${pageContext.request.contextPath}/camera/list" class="btn btn-outline-primary">
            <i class="bi bi-arrow-left"></i> 返回摄像头列表
        </a>
        <button class="btn btn-outline-danger" id="deleteCameraBtn" data-camera-id="${camera.id}" data-camera-name="${camera.name}">
            <i class="bi bi-trash me-1"></i> 删除摄像头
        </button>
    </div>
    
    <!-- 摄像头标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <h2>
                <span class="camera-status ${camera.status == 1 ? 'camera-online' : 'camera-offline'}"></span>
                ${camera.name}
                <small class="text-muted fs-6 ms-2">${camera.brand} ${camera.model}</small>
            </h2>
            <p class="text-muted">
                <i class="bi bi-geo-alt me-1"></i> ${camera.location}
                <c:if test="${camera.room != null}">
                    <span class="mx-2">|</span>
                    <i class="bi bi-building me-1"></i> ${camera.room.roomNumber} (${camera.room.floorNumber}楼)
                </c:if>
            </p>
        </div>
    </div>
    
    <!-- 摄像头内容 -->
    <div class="row">
        <!-- 左侧：视频流和控制面板 -->
        <div class="col-lg-8 mb-4">
            <!-- 视频流 -->
            <div class="card camera-info-card mb-4">
                <div class="card-header bg-white p-3">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-camera-video me-2 text-primary"></i>视频流
                    </h5>
                </div>
                <div class="card-body p-3">
                    <div class="camera-stream-container">
                        <c:choose>
                            <c:when test="${camera.status == 1}">
                                <img src="${pageContext.request.contextPath}/static/images/camera-stream.jpg" alt="摄像头视频流">
                            </c:when>
                            <c:otherwise>
                                <div class="stream-overlay">
                                    <i class="bi bi-camera-video-off mb-3 fs-1"></i>
                                    <h4>摄像头离线</h4>
                                    <p>请连接摄像头后查看视频流</p>
                                </div>
                            </c:otherwise>
                        </c:choose>
                    </div>
                </div>
                <div class="card-footer bg-white p-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <span class="badge ${camera.status == 1 ? 'bg-success' : 'bg-danger'}">
                                ${camera.status == 1 ? '在线' : '离线'}
                            </span>
                            <c:if test="${camera.status == 1}">
                                <small class="text-muted ms-2">最后在线时间: ${camera.lastOnlineTime}</small>
                            </c:if>
                        </div>
                        <div>
                            <c:choose>
                                <c:when test="${camera.status == 0}">
                                    <button id="connectBtn" class="btn btn-success" data-camera-id="${camera.id}" data-action="connect">
                                        <i class="bi bi-wifi me-1"></i> 连接摄像头
                                    </button>
                                </c:when>
                                <c:otherwise>
                                    <button id="connectBtn" class="btn btn-danger" data-camera-id="${camera.id}" data-action="disconnect">
                                        <i class="bi bi-wifi-off me-1"></i> 断开摄像头
                                    </button>
                                    <button id="viewStreamBtn" class="btn btn-primary ms-2" data-camera-id="${camera.id}">
                                        <i class="bi bi-play-circle me-1"></i> 全屏查看
                                    </button>
                                </c:otherwise>
                            </c:choose>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 控制面板 -->
            <div class="card camera-info-card mb-4">
                <div class="card-header bg-white p-3">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-joystick me-2 text-primary"></i>控制面板
                    </h5>
                </div>
                <div class="card-body p-3">
                    <div class="camera-control-panel">
                        <div class="row text-center">
                            <div class="col-12 mb-3">
                                <button class="btn btn-outline-primary control-btn mx-auto" data-camera-id="${camera.id}" data-action="tilt_up">
                                    <i class="bi bi-arrow-up"></i>
                                </button>
                            </div>
                            <div class="col-12 mb-3">
                                <div class="d-flex justify-content-center">
                                    <button class="btn btn-outline-primary control-btn" data-camera-id="${camera.id}" data-action="pan_left">
                                        <i class="bi bi-arrow-left"></i>
                                    </button>
                                    <button class="btn btn-outline-primary control-btn mx-2" data-camera-id="${camera.id}" data-action="home">
                                        <i class="bi bi-house"></i>
                                    </button>
                                    <button class="btn btn-outline-primary control-btn" data-camera-id="${camera.id}" data-action="pan_right">
                                        <i class="bi bi-arrow-right"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="col-12 mb-3">
                                <button class="btn btn-outline-primary control-btn mx-auto" data-camera-id="${camera.id}" data-action="tilt_down">
                                    <i class="bi bi-arrow-down"></i>
                                </button>
                            </div>
                            <div class="col-12">
                                <div class="d-flex justify-content-center">
                                    <button class="btn btn-outline-primary control-btn" data-camera-id="${camera.id}" data-action="zoom_out">
                                        <i class="bi bi-dash-lg"></i>
                                    </button>
                                    <button class="btn btn-outline-primary control-btn ms-3" data-camera-id="${camera.id}" data-action="zoom_in">
                                        <i class="bi bi-plus-lg"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 右侧：摄像头信息和人员记录 -->
        <div class="col-lg-4">
            <!-- 摄像头信息 -->
            <div class="card camera-info-card mb-4">
                <div class="card-header bg-white p-3">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-info-circle me-2 text-primary"></i>摄像头信息
                    </h5>
                </div>
                <div class="card-body p-3">
                    <table class="table table-borderless">
                        <tbody>
                            <tr>
                                <th class="ps-0" style="width: 120px;">IP地址</th>
                                <td>${camera.ipAddress}</td>
                            </tr>
                            <tr>
                                <th class="ps-0">端口</th>
                                <td>${camera.port}</td>
                            </tr>
                            <tr>
                                <th class="ps-0">RTSP URL</th>
                                <td><small class="text-muted">${camera.rtspUrl}</small></td>
                            </tr>
                            <tr>
                                <th class="ps-0">品牌</th>
                                <td>${camera.brand}</td>
                            </tr>
                            <tr>
                                <th class="ps-0">型号</th>
                                <td>${camera.model}</td>
                            </tr>
                            <tr>
                                <th class="ps-0">位置</th>
                                <td>${camera.location}</td>
                            </tr>
                            <tr>
                                <th class="ps-0">所属房间</th>
                                <td>
                                    <c:choose>
                                        <c:when test="${camera.room != null}">
                                            ${camera.room.roomNumber} (${camera.room.floorNumber}楼)
                                        </c:when>
                                        <c:otherwise>
                                            <span class="text-muted">未分配房间</span>
                                        </c:otherwise>
                                    </c:choose>
                                </td>
                            </tr>
                            <tr>
                                <th class="ps-0">创建时间</th>
                                <td><small class="text-muted">${camera.createTime}</small></td>
                            </tr>
                            <tr>
                                <th class="ps-0">最后更新</th>
                                <td><small class="text-muted">${camera.updateTime}</small></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- 人员记录 -->
            <c:if test="${camera.roomId > 0}">
                <div class="card camera-info-card mb-4">
                    <div class="card-header bg-white p-3">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-people me-2 text-primary"></i>人员记录
                        </h5>
                    </div>
                    <div class="card-body p-3">
                        <c:choose>
                            <c:when test="${not empty personRecords}">
                                <!-- 当前人数 -->
                                <div class="alert ${latestRecord.personCount > 0 ? 'alert-success' : 'alert-secondary'} mb-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-0">当前人数</h6>
                                            <small class="text-muted">最后更新: ${latestRecord.recordTime}</small>
                                        </div>
                                        <span class="person-count-badge ${latestRecord.personCount > 0 ? 'bg-success' : 'bg-secondary'}">
                                            ${latestRecord.personCount} 人
                                        </span>
                                    </div>
                                </div>
                                
                                <!-- 人数统计图表 -->
                                <div style="height: 200px;">
                                    <canvas id="personCountChart"></canvas>
                                </div>
                                
                                <!-- 历史记录 -->
                                <h6 class="mt-4 mb-3">历史记录</h6>
                                <div class="list-group">
                                    <c:forEach items="${personRecords}" var="record" varStatus="status" begin="0" end="4">
                                        <div class="list-group-item list-group-item-action person-record-card">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <h6 class="mb-1">${record.personCount} 人</h6>
                                                    <small class="text-muted">${record.recordTime}</small>
                                                </div>
                                                <c:if test="${record.imageUrl != null}">
                                                    <button class="btn btn-sm btn-outline-primary">
                                                        <i class="bi bi-image"></i>
                                                    </button>
                                                </c:if>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </c:when>
                            <c:otherwise>
                                <div class="alert alert-info text-center py-4">
                                    <i class="bi bi-exclamation-circle fs-1 mb-3 d-block"></i>
                                    <h6>暂无人员记录</h6>
                                    <p class="mb-0">摄像头连接后将自动记录人员信息</p>
                                </div>
                            </c:otherwise>
                        </c:choose>
                    </div>
                </div>
            </c:if>
        </div>
    </div>
</div>
