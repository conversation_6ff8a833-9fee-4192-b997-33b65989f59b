# 流服务器自动重启问题修复说明

## 问题描述

用户在摄像头管理页面点击"停止流服务器"后，控制台显示信息仍然在不断重新启动，导致用户无法真正停止流服务器。

## 问题原因分析

### 1. 自动启动机制冲突

在 `CameraListServlet` 中，每次用户访问摄像头管理页面时，系统都会检查流服务器状态：

```java
// 自动启动流服务器（如果未运行）
if (!videoStreamService.isStreamServerRunning()) {
    boolean startResult = videoStreamService.startStreamServer();
    // ...
}
```

这导致即使用户手动停止了流服务器，下次访问页面时又会被自动启动。

### 2. Node.js 流服务器内部重启机制

在 `stream-server.js` 中，FFmpeg 进程退出时会自动重启：

```javascript
ffmpeg.on('close', (code) => {
    const stream = activeStreams.get(streamId);
    if (stream && stream.autoRestart !== false) {
        console.log(`自动重启流: ${streamId}`);
        setTimeout(() => {
            if (activeStreams.has(streamId)) {
                startRTSPStream(rtspUrl, streamId, format).catch(console.error);
            }
        }, 2000);
    }
});
```

## 解决方案

### 1. 添加用户手动停止标志

在 `VideoStreamServiceImpl` 中添加静态变量来跟踪用户的手动停止操作：

```java
// 用户手动停止标志
private static boolean userManuallyStopped = false;
```

### 2. 修改停止方法

在 `stopStreamServer()` 方法中设置手动停止标志：

```java
@Override
public boolean stopStreamServer() {
    System.out.println("尝试停止流服务器...");
    
    // 设置用户手动停止标志
    userManuallyStopped = true;
    
    // ... 停止逻辑
}
```

### 3. 修改启动方法

在 `startStreamServer()` 方法中重置手动停止标志：

```java
@Override
public boolean startStreamServer() {
    // 检查是否已经运行
    if (checkStreamServerHealth()) {
        userManuallyStopped = false; // 重置手动停止标志
        return true;
    }
    
    // 启动成功后重置标志
    if (isHealthy) {
        userManuallyStopped = false; // 重置手动停止标志
        return true;
    }
}
```

### 4. 添加检查方法

添加方法来检查用户是否手动停止了服务器：

```java
/**
 * 检查用户是否手动停止了流服务器
 * @return 是否手动停止
 */
public boolean isUserManuallyStopped() {
    return userManuallyStopped;
}
```

### 5. 修改自动启动逻辑

在 `CameraListServlet` 中修改自动启动逻辑，只有在用户没有手动停止的情况下才自动启动：

```java
// 自动启动流服务器（如果未运行且用户未手动停止）
boolean isRunning = videoStreamService.isStreamServerRunning();
boolean userManuallyStopped = videoStreamService.isUserManuallyStopped();

if (!isRunning && !userManuallyStopped) {
    // 尝试启动
    boolean startResult = videoStreamService.startStreamServer();
    // ...
} else if (!isRunning && userManuallyStopped) {
    // 用户手动停止，不自动启动
    request.setAttribute("streamServerMessage", "视频流服务器已停止（用户手动停止）");
    request.setAttribute("streamServerStatus", "info");
}
```

## 修复效果

### 修复前
1. 用户点击"停止流服务器"
2. 流服务器停止
3. 用户刷新页面或重新访问摄像头管理页面
4. 系统检测到流服务器未运行，自动启动
5. 流服务器又开始运行

### 修复后
1. 用户点击"停止流服务器"
2. 流服务器停止，设置手动停止标志
3. 用户刷新页面或重新访问摄像头管理页面
4. 系统检测到用户手动停止，不自动启动
5. 流服务器保持停止状态
6. 只有用户手动点击"启动"按钮时才会重新启动

## 用户体验改进

### 1. 状态提示优化
- 运行中：显示"视频流服务器运行正常"
- 用户手动停止：显示"视频流服务器已停止（用户手动停止）"
- 自动启动：显示"视频流服务器已自动启动"

### 2. 操作逻辑清晰
- 用户手动停止后，系统不会自动启动
- 用户手动启动后，重置停止标志，恢复自动启动功能
- 保持用户操作意图的一致性

## 技术细节

### 1. 线程安全
使用静态变量 `userManuallyStopped` 在整个应用生命周期中保持状态。

### 2. 状态重置时机
- 用户手动启动时重置
- 检测到服务器已运行时重置
- 启动成功后重置

### 3. 接口扩展
在 `VideoStreamService` 接口中添加新方法：

```java
/**
 * 检查用户是否手动停止了流服务器
 * @return 是否手动停止
 */
boolean isUserManuallyStopped();
```

## 测试建议

### 1. 基本功能测试
- 启动流服务器
- 停止流服务器
- 重启流服务器

### 2. 自动启动测试
- 停止服务器后刷新页面，确认不会自动启动
- 启动服务器后刷新页面，确认保持运行状态

### 3. 状态显示测试
- 验证各种状态下的提示信息是否正确
- 确认状态指示器显示正确

## 注意事项

1. **应用重启**：应用重启后 `userManuallyStopped` 标志会重置为 `false`
2. **多用户环境**：当前实现是全局状态，适用于单用户或管理员操作场景
3. **持久化**：如需在应用重启后保持状态，可考虑将标志存储到数据库或配置文件

## 补充修复：Node.js 流服务器内部重试限制

### 问题发现
在修复 Java 层面的自动启动问题后，发现 Node.js 流服务器内部也存在无限重试问题：
- FFmpeg 进程立即退出（通常是 RTSP 连接失败）
- Node.js 检测到进程退出，触发自动重启
- 无限循环重启，导致控制台日志刷屏

### 解决方案
1. **添加重试计数限制**：最多重试 3 次
2. **递增延迟时间**：每次重试延迟时间递增（5秒、10秒、15秒）
3. **详细错误日志**：识别常见错误类型并提供具体提示
4. **停止所有流功能**：提供强制停止所有流的能力

### 代码修改
```javascript
// 限制重试次数
if (stream.retryCount <= 3) {
    console.log(`自动重启流: ${streamId} (第${stream.retryCount}次重试)`);
    setTimeout(() => {
        // 重启逻辑
    }, 5000 * stream.retryCount); // 递增延迟
} else {
    console.error(`流 ${streamId} 重试次数超限，停止自动重启`);
    activeStreams.delete(streamId);
}
```

### 新增功能
- **停止所有流 API**：`POST /api/stream/stop-all`
- **前端停止所有流按钮**：在流服务器管理面板中
- **详细错误诊断**：识别 RTSP 连接失败、权限错误等

---

**修复完成时间**：2025-01-27
**影响范围**：流服务器管理功能
**向后兼容性**：完全兼容，不影响现有功能
