<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<jsp:include page="/WEB-INF/views/common/layout.jsp">
    <jsp:param name="title" value="视频流" />
    <jsp:param name="content" value="/WEB-INF/views/camera/stream-content.jsp" />
    <jsp:param name="additionalStyles" value="
        /* 视频流页面样式 */
        .stream-container {
            background-color: #000;
            border-radius: 8px;
            overflow: hidden;
            position: relative;
            height: calc(100vh - 200px);
            min-height: 500px;
        }
        .stream-container img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
        .stream-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
        }
        .stream-controls {
            position: absolute;
            bottom: 20px;
            left: 0;
            width: 100%;
            padding: 0 20px;
            display: flex;
            justify-content: center;
            z-index: 10;
        }
        .stream-controls .btn {
            margin: 0 5px;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
        }
        .stream-controls .btn:hover {
            background-color: rgba(255, 255, 255, 0.3);
        }
        .stream-controls .btn-primary {
            background-color: rgba(13, 110, 253, 0.7);
        }
        .stream-controls .btn-primary:hover {
            background-color: rgba(13, 110, 253, 0.9);
        }
        .stream-info {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            z-index: 10;
            background-color: rgba(0, 0, 0, 0.5);
            padding: 10px 15px;
            border-radius: 5px;
        }
        .camera-status {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
        }
        .camera-online {
            background-color: #28a745;
        }
        .camera-offline {
            background-color: #dc3545;
        }
    " />
    <jsp:param name="scripts" value="
        <script src='https://cdn.jsdelivr.net/npm/hls.js@latest'></script>
        <script>
            let hlsPlayer = null;
            let currentStreamUrl = null;

            // 清理视频缓存
            function clearVideoCache() {
                console.log('清理视频缓存...');

                // 清理现有播放器
                if (hlsPlayer) {
                    hlsPlayer.destroy();
                    hlsPlayer = null;
                }

                // 重置视频元素
                const video = document.getElementById('videoPlayer');
                if (video) {
                    video.src = '';
                    video.load();
                }

                // 清理当前流URL
                currentStreamUrl = null;

                console.log('视频缓存清理完成');
            }

            // 页面加载完成后执行
            document.addEventListener('DOMContentLoaded', function() {
                // 获取视频流URL
                const cameraId = '${camera.id}';

                // 清理视频缓存
                clearVideoCache();

                // 如果摄像头在线，检查流状态
                if (${camera.status} === 1) {
                    checkStreamStatus(cameraId);
                }
                
                // 流控制按钮事件
                const startStreamBtn = document.getElementById('startStreamBtn');
                if (startStreamBtn) {
                    startStreamBtn.addEventListener('click', () => startStream(cameraId));
                }

                const stopStreamBtn = document.getElementById('stopStreamBtn');
                if (stopStreamBtn) {
                    stopStreamBtn.addEventListener('click', () => stopStream(cameraId));
                }

                const restartStreamBtn = document.getElementById('restartStreamBtn');
                if (restartStreamBtn) {
                    restartStreamBtn.addEventListener('click', () => restartStream(cameraId));
                }

                const refreshStreamBtn = document.getElementById('refreshStreamBtn');
                if (refreshStreamBtn) {
                    refreshStreamBtn.addEventListener('click', () => checkStreamStatus(cameraId));
                }

                // 控制按钮点击事件
                document.querySelectorAll('.control-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const action = this.getAttribute('data-action');
                        controlCamera(cameraId, action);
                    });
                });

                // 连接/断开摄像头按钮点击事件
                const connectBtn = document.getElementById('connectBtn');
                if (connectBtn) {
                    connectBtn.addEventListener('click', function() {
                        const action = this.getAttribute('data-action');

                        fetch('${pageContext.request.contextPath}/camera/control', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                            body: 'cameraId=' + cameraId + '&action=' + action
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                alert(data.message);
                                // 刷新页面
                                window.location.reload();
                            } else {
                                alert('操作失败: ' + data.message);
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            alert('操作失败，请稍后重试');
                        });
                    });
                }

                // 断开摄像头按钮事件
                const disconnectBtn = document.getElementById('disconnectBtn');
                if (disconnectBtn) {
                    disconnectBtn.addEventListener('click', function() {
                        const action = this.getAttribute('data-action');

                        fetch('${pageContext.request.contextPath}/camera/control', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                            body: 'cameraId=' + cameraId + '&action=' + action
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                alert(data.message);
                                // 刷新页面
                                window.location.reload();
                            } else {
                                alert('操作失败: ' + data.message);
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            alert('操作失败，请稍后重试');
                        });
                    });
                }
                
                // 全屏按钮点击事件
                const fullscreenBtn = document.getElementById('fullscreenBtn');
                if (fullscreenBtn) {
                    fullscreenBtn.addEventListener('click', function() {
                        const streamContainer = document.querySelector('.stream-container');
                        if (streamContainer) {
                            if (streamContainer.requestFullscreen) {
                                streamContainer.requestFullscreen();
                            } else if (streamContainer.webkitRequestFullscreen) { /* Safari */
                                streamContainer.webkitRequestFullscreen();
                            } else if (streamContainer.msRequestFullscreen) { /* IE11 */
                                streamContainer.msRequestFullscreen();
                            }
                        }
                    });
                }
            });

            // 页面离开时清理资源
            window.addEventListener('beforeunload', function() {
                clearVideoCache();
            });

            // 页面隐藏时暂停播放
            document.addEventListener('visibilitychange', function() {
                const video = document.getElementById('videoPlayer');
                if (document.hidden) {
                    if (video && !video.paused) {
                        video.pause();
                    }
                } else {
                    // 页面重新可见时，如果有流URL则重新加载
                    if (currentStreamUrl && video) {
                        video.play().catch(e => {
                            console.log('恢复播放失败:', e.message);
                        });
                    }
                }
            });

            // 检查流状态
            function checkStreamStatus(cameraId) {
                updateStatusMessage('正在检查流状态...');

                fetch('${pageContext.request.contextPath}/camera/stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'cameraId=' + cameraId + '&action=getStreamUrl'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.streamUrl) {
                        updateStatusMessage('检测到活动流，正在加载播放器...');
                        loadHLSPlayer(data.streamUrl);
                        showStreamControls();
                    } else {
                        updateStatusMessage('未检测到活动流，请启动视频流');
                        hideStreamControls();
                    }
                })
                .catch(error => {
                    console.error('检查流状态失败:', error);
                    updateStatusMessage('检查流状态失败，请稍后重试');
                });
            }

            // 启动视频流
            function startStream(cameraId) {
                updateStatusMessage('正在启动视频流...');

                fetch('${pageContext.request.contextPath}/camera/stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'cameraId=' + cameraId + '&action=startStream'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateStatusMessage('视频流启动成功，正在加载播放器...');
                        if (data.streamUrl) {
                            setTimeout(() => {
                                loadHLSPlayer(data.streamUrl);
                                showStreamControls();
                            }, 3000); // 等待3秒让流稳定
                        } else {
                            setTimeout(() => checkStreamStatus(cameraId), 3000);
                        }
                    } else {
                        updateStatusMessage('启动视频流失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('启动视频流失败:', error);
                    updateStatusMessage('启动视频流失败，请稍后重试');
                });
            }

            // 停止视频流
            function stopStream(cameraId) {
                updateStatusMessage('正在停止视频流...');

                fetch('${pageContext.request.contextPath}/camera/stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'cameraId=' + cameraId + '&action=stopStream'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateStatusMessage('视频流已停止');
                        stopHLSPlayer();
                        hideStreamControls();
                    } else {
                        updateStatusMessage('停止视频流失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('停止视频流失败:', error);
                    updateStatusMessage('停止视频流失败，请稍后重试');
                });
            }

            // 重启视频流
            function restartStream(cameraId) {
                updateStatusMessage('正在重启视频流...');

                fetch('${pageContext.request.contextPath}/camera/stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'cameraId=' + cameraId + '&action=restartStream'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateStatusMessage('视频流重启成功，正在加载播放器...');
                        if (data.streamUrl) {
                            setTimeout(() => {
                                loadHLSPlayer(data.streamUrl);
                                showStreamControls();
                            }, 5000); // 等待5秒让流稳定
                        } else {
                            setTimeout(() => checkStreamStatus(cameraId), 5000);
                        }
                    } else {
                        updateStatusMessage('重启视频流失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('重启视频流失败:', error);
                    updateStatusMessage('重启视频流失败，请稍后重试');
                });
            }
            
            // 加载HLS播放器
            function loadHLSPlayer(streamUrl) {
                const video = document.getElementById('videoPlayer');
                const streamStatus = document.getElementById('streamStatus');

                if (!streamUrl) {
                    console.error('流URL为空');
                    return;
                }

                // 添加时间戳防缓存
                const timestamp = new Date().getTime();
                const urlWithTimestamp = streamUrl + '?t=' + timestamp;
                console.log('加载HLS流:', urlWithTimestamp);

                // 清理现有播放器和视频元素
                if (hlsPlayer) {
                    hlsPlayer.destroy();
                    hlsPlayer = null;
                }

                // 重置视频元素
                video.src = '';
                video.load();

                if (Hls.isSupported()) {
                    hlsPlayer = new Hls({
                        debug: false,
                        enableWorker: true,
                        lowLatencyMode: true,
                        backBufferLength: 10,
                        maxBufferLength: 10,
                        maxMaxBufferLength: 15,
                        liveSyncDurationCount: 1,
                        liveMaxLatencyDurationCount: 3,
                        manifestLoadingTimeOut: 10000,
                        manifestLoadingMaxRetry: 4,
                        manifestLoadingRetryDelay: 500,
                        levelLoadingTimeOut: 10000,
                        levelLoadingMaxRetry: 4,
                        levelLoadingRetryDelay: 500,
                        fragLoadingTimeOut: 20000,
                        fragLoadingMaxRetry: 6,
                        fragLoadingRetryDelay: 500,
                        startLevel: -1,
                        capLevelToPlayerSize: false,
                        // 防缓存配置
                        xhrSetup: function(xhr, url) {
                            xhr.setRequestHeader('Cache-Control', 'no-cache');
                            xhr.setRequestHeader('Pragma', 'no-cache');
                        }
                    });

                    hlsPlayer.loadSource(urlWithTimestamp);
                    hlsPlayer.attachMedia(video);

                    hlsPlayer.on(Hls.Events.MANIFEST_PARSED, () => {
                        console.log('HLS清单解析完成，开始播放');
                        updateStatusMessage('视频流加载成功！');

                        // 隐藏状态覆盖层，显示视频
                        streamStatus.style.display = 'none';
                        video.style.display = 'block';

                        video.play().catch(e => {
                            console.log('自动播放失败:', e.message);
                            updateStatusMessage('视频已准备就绪，请点击播放按钮');
                        });
                    });

                    hlsPlayer.on(Hls.Events.ERROR, (event, data) => {
                        console.log('HLS错误:', data.type, '-', data.details);

                        if (data.fatal) {
                            switch(data.type) {
                                case Hls.ErrorTypes.NETWORK_ERROR:
                                    console.log('网络错误，尝试重新加载...');
                                    updateStatusMessage('网络错误，正在重试...');
                                    setTimeout(() => {
                                        if (hlsPlayer) {
                                            hlsPlayer.startLoad();
                                        }
                                    }, 1000);
                                    break;
                                case Hls.ErrorTypes.MEDIA_ERROR:
                                    console.log('媒体错误，尝试恢复...');
                                    updateStatusMessage('媒体错误，正在恢复...');
                                    if (hlsPlayer) {
                                        hlsPlayer.recoverMediaError();
                                    }
                                    break;
                                default:
                                    console.log('致命错误，等待流重启...');
                                    updateStatusMessage('播放错误，请尝试重启视频流');
                                    break;
                            }
                        }
                    });

                } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
                    console.log('使用Safari原生HLS支持');
                    video.src = urlWithTimestamp;
                    video.addEventListener('loadedmetadata', () => {
                        console.log('元数据加载完成，开始播放');
                        updateStatusMessage('视频流加载成功！');

                        streamStatus.style.display = 'none';
                        video.style.display = 'block';
                        video.play();
                    });
                } else {
                    updateStatusMessage('浏览器不支持HLS播放');
                }

                currentStreamUrl = urlWithTimestamp;
            }

            // 停止HLS播放器
            function stopHLSPlayer() {
                const video = document.getElementById('videoPlayer');
                const streamStatus = document.getElementById('streamStatus');

                if (hlsPlayer) {
                    hlsPlayer.destroy();
                    hlsPlayer = null;
                }

                video.src = '';
                video.style.display = 'none';
                streamStatus.style.display = 'flex';

                currentStreamUrl = null;
            }

            // 更新状态消息
            function updateStatusMessage(message) {
                const statusMessage = document.getElementById('statusMessage');
                if (statusMessage) {
                    statusMessage.textContent = message;
                }
                console.log('状态:', message);
            }

            // 显示流控制按钮
            function showStreamControls() {
                const stopBtn = document.getElementById('stopStreamBtn');
                const restartBtn = document.getElementById('restartStreamBtn');

                if (stopBtn) stopBtn.style.display = 'block';
                if (restartBtn) restartBtn.style.display = 'block';
            }

            // 隐藏流控制按钮
            function hideStreamControls() {
                const stopBtn = document.getElementById('stopStreamBtn');
                const restartBtn = document.getElementById('restartStreamBtn');

                if (stopBtn) stopBtn.style.display = 'none';
                if (restartBtn) restartBtn.style.display = 'none';
            }

            // 控制摄像头
            function controlCamera(cameraId, action) {
                fetch('${pageContext.request.contextPath}/camera/control', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'cameraId=' + cameraId + '&action=' + action
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        console.log('控制成功:', data.message);
                    } else {
                        console.error('控制失败:', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
            }
        </script>
    " />
</jsp:include>
