# 重启应用程序脚本
# 用于修复Spring MVC映射问题

Write-Host "正在重启EduFusionCenter应用程序..." -ForegroundColor Green

# 1. 停止现有的Tomcat进程
Write-Host "1. 停止现有的Tomcat进程..." -ForegroundColor Yellow
$tomcatProcesses = Get-Process -Name "java" -ErrorAction SilentlyContinue | Where-Object { $_.CommandLine -like "*tomcat*" -or $_.CommandLine -like "*catalina*" }
if ($tomcatProcesses) {
    $tomcatProcesses | Stop-Process -Force
    Write-Host "   已停止Tomcat进程" -ForegroundColor Green
} else {
    Write-Host "   未找到运行中的Tomcat进程" -ForegroundColor Yellow
}

# 2. 清理编译文件
Write-Host "2. 清理编译文件..." -ForegroundColor Yellow
if (Test-Path "target") {
    Remove-Item -Path "target" -Recurse -Force
    Write-Host "   已清理target目录" -ForegroundColor Green
}

# 3. 重新编译项目
Write-Host "3. 重新编译项目..." -ForegroundColor Yellow
try {
    $compileResult = mvn clean compile -q
    if ($LASTEXITCODE -eq 0) {
        Write-Host "   编译成功" -ForegroundColor Green
    } else {
        Write-Host "   编译失败，请检查错误信息" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "   编译过程中出现错误: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 4. 打包WAR文件
Write-Host "4. 打包WAR文件..." -ForegroundColor Yellow
try {
    $packageResult = mvn package -DskipTests -q
    if ($LASTEXITCODE -eq 0) {
        Write-Host "   打包成功" -ForegroundColor Green
    } else {
        Write-Host "   打包失败，请检查错误信息" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "   打包过程中出现错误: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 5. 检查WAR文件是否生成
$warFile = "target\EduFusionCenter.war"
if (Test-Path $warFile) {
    Write-Host "   WAR文件已生成: $warFile" -ForegroundColor Green
} else {
    Write-Host "   WAR文件未找到，打包可能失败" -ForegroundColor Red
    exit 1
}

Write-Host "应用程序重新编译完成！" -ForegroundColor Green
Write-Host "请将 target\EduFusionCenter.war 部署到Tomcat的webapps目录中" -ForegroundColor Cyan
Write-Host "然后启动Tomcat服务器" -ForegroundColor Cyan

# 6. 显示部署说明
Write-Host "`n部署说明:" -ForegroundColor Magenta
Write-Host "1. 将 target\EduFusionCenter.war 复制到 Tomcat的webapps目录" -ForegroundColor White
Write-Host "2. 启动Tomcat服务器" -ForegroundColor White
Write-Host "3. 访问 http://localhost:8080/EduFusionCenter/system/settings 测试系统设置页面" -ForegroundColor White
Write-Host "4. 访问 http://localhost:8080/EduFusionCenter/production/list 测试产线管理页面" -ForegroundColor White

Write-Host "`n如果问题仍然存在，请检查:" -ForegroundColor Magenta
Write-Host "- Tomcat日志文件中的错误信息" -ForegroundColor White
Write-Host "- Spring容器是否正确初始化" -ForegroundColor White
Write-Host "- 控制器是否被正确扫描和注册" -ForegroundColor White
