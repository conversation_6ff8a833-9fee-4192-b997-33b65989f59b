package com.building.controller;

import com.building.model.ProductionLine;
import com.building.service.ProductionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Controller
@RequestMapping("/production")
public class ProductionController {
    private static final Logger logger = LoggerFactory.getLogger(ProductionController.class);

    @Autowired
    private ProductionService productionService;

    // 显示产线管理页面
    @RequestMapping("/list")
    public String list(Model model) {
        logger.info("ProductionController.list() 被调用");
        try {
            List<ProductionLine> productionLines = productionService.getAllProductionLines();
            model.addAttribute("productionLines", productionLines);
            logger.info("成功获取产线列表，返回视图: production/list");
            return "production/list";
        } catch (Exception e) {
            logger.error("访问产线管理页面出错", e);
            return "error/500";
        }
    }

    // 显示产线详情
    @RequestMapping("/{id}")
    public String detail(@PathVariable int id, Model model) {
        logger.info("访问产线详情页面 - id: {}", id);
        try {
            ProductionLine productionLine = productionService.getProductionLine(id);
            if (productionLine != null) {
                model.addAttribute("productionLine", productionLine);
                logger.info("成功获取产线信息");
                return "production/detail";
            }
            logger.info("未找到对应的产线");
            return "error/404";
        } catch (Exception e) {
            logger.error("访问产线详情页面出错", e);
            return "error/500";
        }
    }

    // 添加产线
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @ResponseBody
    public String add(@RequestParam String name, @RequestParam String status) {
        logger.info("添加新产线 - name: {}, status: {}", name, status);
        try {
            ProductionLine productionLine = new ProductionLine();
            productionLine.setName(name);
            productionLine.setStatus(status);
            productionService.addProductionLine(productionLine);
            logger.info("产线添加成功");
            return "success";
        } catch (Exception e) {
            logger.error("添加产线失败", e);
            return "error";
        }
    }

    // 更新产线
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @ResponseBody
    public String update(@RequestParam int id, @RequestParam String name, @RequestParam String status) {
        logger.info("更新产线 - id: {}, name: {}, status: {}", id, name, status);
        try {
            ProductionLine productionLine = new ProductionLine();
            productionLine.setId(id);
            productionLine.setName(name);
            productionLine.setStatus(status);
            productionService.updateProductionLine(productionLine);
            logger.info("产线更新成功");
            return "success";
        } catch (Exception e) {
            logger.error("更新产线失败", e);
            return "error";
        }
    }

    // 删除产线
    @RequestMapping(value = "/delete/{id}", method = RequestMethod.POST)
    @ResponseBody
    public String delete(@PathVariable int id) {
        logger.info("删除产线 - id: {}", id);
        try {
            productionService.deleteProductionLine(id);
            logger.info("产线删除成功");
            return "success";
        } catch (Exception e) {
            logger.error("删除产线失败", e);
            return "error";
        }
    }

    // 获取产线信息（用于编辑）
    @RequestMapping("/get/{id}")
    @ResponseBody
    public ProductionLine get(@PathVariable int id) {
        logger.info("获取产线信息 - id: {}", id);
        try {
            ProductionLine productionLine = productionService.getProductionLine(id);
            logger.info("成功获取产线信息");
            return productionLine;
        } catch (Exception e) {
            logger.error("获取产线信息失败", e);
            return null;
        }
    }
} 