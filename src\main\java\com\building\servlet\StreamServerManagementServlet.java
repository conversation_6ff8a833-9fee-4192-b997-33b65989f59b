package com.building.servlet;

import com.building.service.VideoStreamService;
import com.building.service.impl.VideoStreamServiceImpl;
import com.google.gson.Gson;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 流服务器管理Servlet
 * 负责管理Node.js流服务器的启动、停止和状态监控
 */
@WebServlet("/stream-server/manage")
public class StreamServerManagementServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;
    
    private VideoStreamService videoStreamService;
    private Gson gson;
    
    @Override
    public void init() throws ServletException {
        try {
            videoStreamService = new VideoStreamServiceImpl();
            gson = new Gson();
            System.out.println("StreamServerManagementServlet 初始化成功");
        } catch (Exception e) {
            System.err.println("StreamServerManagementServlet 初始化失败: " + e.getMessage());
            e.printStackTrace();
            throw new ServletException("初始化服务失败", e);
        }
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        // 检查用户是否登录
        HttpSession session = request.getSession();
        if (session.getAttribute("user") == null) {
            response.sendRedirect(request.getContextPath() + "/login.jsp");
            return;
        }
        
        // 设置响应类型
        response.setContentType("application/json;charset=UTF-8");
        
        String action = request.getParameter("action");
        Map<String, Object> result = new HashMap<>();
        
        try {
            if ("status".equals(action)) {
                // 获取流服务器状态
                boolean isRunning = videoStreamService.isStreamServerRunning();
                String healthDetails = videoStreamService.getStreamServerHealthDetails();
                
                result.put("success", true);
                result.put("isRunning", isRunning);
                result.put("healthDetails", healthDetails);
                result.put("message", isRunning ? "流服务器运行中" : "流服务器未运行");
                
            } else {
                result.put("success", false);
                result.put("message", "不支持的操作: " + action);
            }
            
        } catch (Exception e) {
            System.err.println("获取流服务器状态失败: " + e.getMessage());
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "获取状态失败: " + e.getMessage());
        }
        
        // 返回JSON响应
        response.getWriter().write(gson.toJson(result));
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        // 检查用户是否登录
        HttpSession session = request.getSession();
        if (session.getAttribute("user") == null) {
            response.sendRedirect(request.getContextPath() + "/login.jsp");
            return;
        }
        
        // 设置响应类型
        response.setContentType("application/json;charset=UTF-8");
        
        String action = request.getParameter("action");
        Map<String, Object> result = new HashMap<>();
        
        try {
            if ("start".equals(action)) {
                // 启动流服务器
                System.out.println("收到启动流服务器请求");
                boolean success = videoStreamService.startStreamServer();
                
                result.put("success", success);
                result.put("message", success ? "流服务器启动成功" : "流服务器启动失败");
                
                if (success) {
                    // 等待一段时间确保服务器完全启动
                    Thread.sleep(2000);
                    result.put("healthDetails", videoStreamService.getStreamServerHealthDetails());
                }
                
            } else if ("stop".equals(action)) {
                // 停止流服务器
                System.out.println("收到停止流服务器请求");
                boolean success = videoStreamService.stopStreamServer();
                
                result.put("success", success);
                result.put("message", success ? "流服务器停止成功" : "流服务器停止失败");
                
            } else if ("restart".equals(action)) {
                // 重启流服务器
                System.out.println("收到重启流服务器请求");

                // 先停止
                boolean stopSuccess = videoStreamService.stopStreamServer();
                if (stopSuccess) {
                    // 等待一段时间
                    Thread.sleep(3000);

                    // 再启动
                    boolean startSuccess = videoStreamService.startStreamServer();

                    result.put("success", startSuccess);
                    result.put("message", startSuccess ? "流服务器重启成功" : "流服务器重启失败");

                    if (startSuccess) {
                        Thread.sleep(2000);
                        result.put("healthDetails", videoStreamService.getStreamServerHealthDetails());
                    }
                } else {
                    result.put("success", false);
                    result.put("message", "停止流服务器失败，无法重启");
                }

            } else if ("stop-all-streams".equals(action)) {
                // 停止所有流
                System.out.println("收到停止所有流请求");
                boolean success = videoStreamService.stopAllStreams();

                result.put("success", success);
                result.put("message", success ? "所有流停止成功" : "停止所有流失败");

            } else {
                result.put("success", false);
                result.put("message", "不支持的操作: " + action);
            }
            
        } catch (Exception e) {
            System.err.println("流服务器管理操作失败: " + e.getMessage());
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "操作失败: " + e.getMessage());
        }
        
        // 返回JSON响应
        response.getWriter().write(gson.toJson(result));
    }
}
